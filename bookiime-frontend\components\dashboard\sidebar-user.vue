<template>
  <div class="relative">
    <!-- User Info -->
    <button
      @click="showDropdown = !showDropdown"
      :class="userButtonClasses"
    >
      <!-- Avatar -->
      <div class="w-10 h-10 bg-gradient-to-br from-primary-100 to-primary-200 rounded-full flex items-center justify-center flex-shrink-0 shadow-sm">
        <img
          v-if="user?.avatar"
          :src="user.avatar"
          :alt="displayName"
          class="w-10 h-10 rounded-full object-cover"
        />
        <Icon
          v-else
          name="lucide:user"
          class="w-5 h-5 text-primary-600"
        />
      </div>
      
      <!-- User Details (when not collapsed) -->
      <div v-if="!collapsed" class="flex-1 text-left">
        <p class="text-sm font-semibold text-neutral-900 truncate">
          {{ displayName }}
        </p>
        <p class="text-xs text-neutral-600 truncate">
          {{ user?.email }}
        </p>
      </div>
      
      <!-- Dropdown Arrow (when not collapsed) -->
      <Icon 
        v-if="!collapsed"
        name="lucide:chevron-up" 
        :class="chevronClasses"
      />
    </button>

    <!-- Dropdown Menu -->
    <Transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="opacity-0 translate-y-1"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="opacity-100 translate-y-0"
      leave-to-class="opacity-0 translate-y-1"
    >
      <div
        v-if="showDropdown"
        :class="dropdownClasses"
      >
        <div class="py-1">
          <Button
            variant="ghost"
            size="sm"
            class="w-full justify-start px-4 py-3 text-sm font-medium text-neutral-700 hover:bg-gradient-to-r hover:from-neutral-50 hover:to-neutral-100 transition-all duration-200 rounded-lg mx-2"
            @click="navigateToProfile"
          >
            <Icon name="lucide:user" class="w-4 h-4 mr-3" />
            Profile
          </Button>

          <Button
            variant="ghost"
            size="sm"
            class="w-full justify-start px-4 py-3 text-sm font-medium text-neutral-700 hover:bg-gradient-to-r hover:from-neutral-50 hover:to-neutral-100 transition-all duration-200 rounded-lg mx-2"
            @click="navigateToSettings"
          >
            <Icon name="lucide:settings" class="w-4 h-4 mr-3" />
            Settings
          </Button>

          <Separator class="my-2 mx-2" />

          <Button
            variant="ghost"
            size="sm"
            class="w-full justify-start px-4 py-3 text-sm font-medium text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-red-100 transition-all duration-200 rounded-lg mx-2"
            @click="handleLogout"
          >
            <Icon name="lucide:log-out" class="w-4 h-4 mr-3" />
            Sign Out
          </Button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
interface User {
  id?: string
  name?: string
  email?: string
  avatar?: string
}

interface Props {
  user: User | null
  collapsed: boolean
}

interface Emits {
  (e: 'logout'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Reactive state
const showDropdown = ref(false)

// Computed properties
const displayName = computed(() => {
  if (props.user?.name) {
    return props.user.name
  }
  
  if (props.user?.email) {
    const emailName = props.user.email.split('@')[0]
    return emailName.charAt(0).toUpperCase() + emailName.slice(1)
  }
  
  return 'User'
})

const userButtonClasses = computed(() => [
  'flex items-center w-full p-3 rounded-xl transition-all duration-200 hover:bg-white/50 hover:shadow-md hover:transform hover:scale-105',
  props.collapsed ? 'justify-center' : 'space-x-3'
])

const chevronClasses = computed(() => [
  'w-4 h-4 text-neutral-400 transition-transform duration-200',
  showDropdown.value ? 'rotate-180' : 'rotate-0'
])

const dropdownClasses = computed(() => [
  'absolute bottom-full left-0 mb-3 bg-white/95 backdrop-blur-md rounded-xl shadow-xl border border-white/20 py-2 z-50',
  props.collapsed ? 'w-52' : 'w-full'
])

// Methods
const navigateToProfile = () => {
  showDropdown.value = false
  navigateTo('/dashboard/profile')
}

const navigateToSettings = () => {
  showDropdown.value = false
  navigateTo('/dashboard/settings')
}

const handleLogout = () => {
  showDropdown.value = false
  emit('logout')
}

// Close dropdown when clicking outside
onMounted(() => {
  const handleClickOutside = (event: Event) => {
    const target = event.target as HTMLElement
    if (!target.closest('.relative')) {
      showDropdown.value = false
    }
  }
  
  document.addEventListener('click', handleClickOutside)
  
  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })
})
</script>
