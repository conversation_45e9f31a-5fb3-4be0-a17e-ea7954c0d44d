<template>
  <header class="bg-gradient-to-r from-neutral-50 to-neutral-100 sticky top-0 z-30">
    <div class="p-4">
      <div class="flex items-center justify-between gap-4">
        <!-- Individual Cards -->
        <div class="flex items-center gap-4">
          <!-- Mobile Sidebar Toggle Card -->
          <div class="lg:hidden bg-white/90 backdrop-blur-md rounded-2xl shadow-lg border border-white/20">
            <button
              @click="$emit('toggle-mobile-sidebar')"
              class="p-3 rounded-2xl text-neutral-600 hover:bg-white/50 hover:shadow-md hover:scale-105 transition-all duration-200"
            >
              <Icon name="lucide:menu" class="w-5 h-5" />
            </button>
          </div>

          <!-- Desktop Sidebar Toggle Card -->
          <div class="hidden lg:block bg-white/90 backdrop-blur-md rounded-2xl shadow-lg border border-white/20">
            <button
              @click="$emit('toggle-sidebar')"
              class="p-3 rounded-2xl text-neutral-600 hover:bg-white/50 hover:shadow-md hover:scale-105 transition-all duration-200"
            >
              <Icon
                :name="sidebarCollapsed ? 'lucide:panel-left-open' : 'lucide:panel-left-close'"
                class="w-5 h-5"
              />
            </button>
          </div>

          <!-- Page Title Card -->
          <div class="bg-white/90 backdrop-blur-md rounded-2xl shadow-lg border border-white/20 px-6 py-4">
            <h1 class="text-lg font-semibold text-neutral-900">
              {{ pageTitle }}
            </h1>
            <p v-if="pageSubtitle" class="text-sm text-neutral-600">
              {{ pageSubtitle }}
            </p>
          </div>
        </div>

        <!-- Individual Cards -->
        <div class="flex items-center gap-4">
          <!-- Search Card (Desktop) -->
          <div class="hidden md:block bg-white/90 backdrop-blur-md rounded-2xl shadow-lg border border-white/20">
            <div class="relative p-3">
              <Icon name="lucide:search" class="absolute left-6 top-1/2 transform -translate-y-1/2 w-4 h-4 text-neutral-400" />
              <input
                type="text"
                placeholder="Search..."
                class="pl-10 pr-4 py-2 border-0 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 bg-white/50 w-64"
              />
            </div>
          </div>

          <!-- Notifications Card -->
          <div class="bg-white/90 backdrop-blur-md rounded-2xl shadow-lg border border-white/20">
            <button class="relative p-3 rounded-2xl text-neutral-600 hover:bg-white/50 hover:shadow-md hover:scale-105 transition-all duration-200">
              <Icon name="lucide:bell" class="w-5 h-5" />
              <span class="absolute top-2 right-2 w-2 h-2 bg-red-500 rounded-full shadow-sm"></span>
            </button>
          </div>

          <!-- User Menu Card (Mobile) -->
          <div class="lg:hidden bg-white/90 backdrop-blur-md rounded-2xl shadow-lg border border-white/20">
            <div class="relative">
              <button
                @click="showMobileUserMenu = !showMobileUserMenu"
                class="flex items-center space-x-2 p-3 rounded-2xl hover:bg-white/50 hover:shadow-md hover:scale-105 transition-all duration-200"
              >
                <div class="w-8 h-8 bg-gradient-to-br from-primary-100 to-primary-200 rounded-full flex items-center justify-center shadow-sm">
                  <img
                    v-if="user?.avatar"
                    :src="user.avatar"
                    :alt="displayName"
                    class="w-8 h-8 rounded-full object-cover"
                  />
                  <Icon
                    v-else
                    name="lucide:user"
                    class="w-4 h-4 text-primary-600"
                  />
                </div>
              </button>

              <!-- Mobile User Dropdown -->
              <Transition
                enter-active-class="transition ease-out duration-200"
                enter-from-class="opacity-0 translate-y-1"
                enter-to-class="opacity-100 translate-y-0"
                leave-active-class="transition ease-in duration-150"
                leave-from-class="opacity-100 translate-y-0"
                leave-to-class="opacity-0 translate-y-1"
              >
                <div
                  v-if="showMobileUserMenu"
                  class="absolute right-0 mt-3 w-52 bg-white/95 backdrop-blur-md rounded-xl shadow-xl border border-white/20 py-2 z-50"
                >
                  <div class="px-4 py-3 border-b border-neutral-200">
                    <p class="text-sm font-semibold text-neutral-900">{{ displayName }}</p>
                    <p class="text-xs text-neutral-600">{{ user?.email }}</p>
                  </div>
                  <NuxtLink
                    to="/dashboard/profile"
                    class="flex items-center px-4 py-3 text-sm font-medium text-neutral-700 hover:bg-white/50 transition-all duration-200 rounded-lg mx-2"
                    @click="showMobileUserMenu = false"
                  >
                    <Icon name="lucide:user" class="w-4 h-4 mr-3" />
                    Profile
                  </NuxtLink>
                  <NuxtLink
                    to="/dashboard/settings"
                    class="flex items-center px-4 py-3 text-sm font-medium text-neutral-700 hover:bg-white/50 transition-all duration-200 rounded-lg mx-2"
                    @click="showMobileUserMenu = false"
                  >
                    <Icon name="lucide:settings" class="w-4 h-4 mr-3" />
                    Settings
                  </NuxtLink>
                  <hr class="my-2 border-neutral-200 mx-2" />
                  <button
                    @click="handleLogout"
                    class="flex items-center w-full px-4 py-3 text-sm font-medium text-red-600 hover:bg-red-50/80 transition-all duration-200 rounded-lg mx-2"
                  >
                    <Icon name="lucide:log-out" class="w-4 h-4 mr-3" />
                    Sign Out
                  </button>
                </div>
              </Transition>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
interface User {
  id?: string
  name?: string
  email?: string
  avatar?: string
}

interface Props {
  user: User | null
  sidebarCollapsed: boolean
}

interface Emits {
  (e: 'toggle-sidebar'): void
  (e: 'toggle-mobile-sidebar'): void
  (e: 'logout'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Composables
const route = useRoute()

// Reactive state
const showMobileUserMenu = ref(false)

// Computed properties
const displayName = computed(() => {
  if (props.user?.name) {
    return props.user.name
  }

  if (props.user?.email) {
    const emailName = props.user.email.split('@')[0]
    return emailName.charAt(0).toUpperCase() + emailName.slice(1)
  }

  return 'User'
})

const pageTitle = computed(() => {
  const routeName = route.name as string
  const titleMap: Record<string, string> = {
    'dashboard': 'Dashboard',
  }

  return titleMap[routeName] || 'Dashboard'
})

const pageSubtitle = computed(() => {
  const routeName = route.name as string
  const subtitleMap: Record<string, string> = {
    'dashboard': `Welcome back, ${displayName.value}`,
  }

  return subtitleMap[routeName] || ''
})

// Methods
const handleLogout = () => {
  showMobileUserMenu.value = false
  emit('logout')
}

onMounted(() => {
  const handleClickOutside = (event: Event) => {
    const target = event.target as HTMLElement
    if (!target.closest('.relative')) {
      showMobileUserMenu.value = false
    }
  }

  document.addEventListener('click', handleClickOutside)

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })
})
</script>
