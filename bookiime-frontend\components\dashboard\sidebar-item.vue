<template>
  <Tooltip
    v-if="collapsed"
    :content="item.label"
    side="right"
    align="center"
    :delay-duration="500"
  >
    <div class="relative group">
      <NuxtLink
        :to="item.route"
        :class="linkClasses"
        @click="$emit('click')"
      >
      <div :class="iconWrapperClasses">
        <Icon
          :name="item.icon"
          :class="iconClasses"
        />
        <!-- Active indicator for collapsed state -->
        <div
          v-if="item.active && collapsed"
          class="absolute -top-1 -right-1 w-3 h-3 bg-white rounded-full border-2 border-primary-500 shadow-sm"
        />
      </div>

      <span
        v-if="!collapsed || isMobile"
        class="transition-all duration-300"
        :class="textClasses"
      >
        {{ item.label }}
      </span>

        <!-- Active indicator for expanded state -->
        <div
          v-if="item.active && !collapsed"
          class="ml-auto w-2 h-2 bg-white rounded-full shadow-sm"
        />
      </NuxtLink>
    </div>
  </Tooltip>

  <div v-else class="relative group">
    <NuxtLink
      :to="item.route"
      :class="linkClasses"
      @click="$emit('click')"
    >
    <div :class="iconWrapperClasses">
      <Icon
        :name="item.icon"
        :class="iconClasses"
      />
      <!-- Active indicator for collapsed state -->
      <div
        v-if="item.active && collapsed"
        class="absolute -top-1 -right-1 w-3 h-3 bg-white rounded-full border-2 border-primary-500 shadow-sm"
      />
    </div>

    <span
      v-if="!collapsed || isMobile"
      class="transition-all duration-300"
      :class="textClasses"
    >
      {{ item.label }}
    </span>

      <!-- Active indicator for expanded state -->
      <div
        v-if="item.active && !collapsed"
        class="ml-auto w-2 h-2 bg-white rounded-full shadow-sm"
      />
    </NuxtLink>
  </div>
</template>

<script setup lang="ts">
interface NavigationItem {
  icon: string
  label: string
  route: string
  active?: boolean
}

interface Props {
  item: NavigationItem
  collapsed: boolean
}

interface Emits {
  (e: 'click'): void
}

const props = defineProps<Props>()
defineEmits<Emits>()

// Check if we're on mobile
const isMobile = ref(false)

onMounted(() => {
  const checkMobile = () => {
    isMobile.value = window.innerWidth < 1024
  }

  checkMobile()
  window.addEventListener('resize', checkMobile)

  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile)
  })
})

// Computed classes following the Open/Closed Principle
const linkClasses = computed(() => [
  'flex items-center rounded-2xl transition-all duration-300 group relative w-full backdrop-blur-sm',
  props.collapsed ? 'justify-center p-3' : 'justify-start space-x-3 px-4 py-3',
  props.item.active
    ? 'bg-gradient-to-r from-primary-500 via-primary-600 to-primary-700 text-white shadow-xl transform scale-105 border border-primary-400/30'
    : 'text-neutral-700 hover:bg-gradient-to-r hover:from-white/60 hover:to-white/40 hover:text-neutral-900 hover:shadow-lg hover:transform hover:scale-[1.02] hover:border hover:border-white/40'
])

const iconWrapperClasses = computed(() => [
  'relative flex items-center justify-center flex-shrink-0',
  props.collapsed ? 'w-8 h-8' : 'w-6 h-6'
])

const iconClasses = computed(() => [
  'transition-all duration-200',
  props.collapsed ? 'w-5 h-5' : 'w-5 h-5',
  props.item.active ? 'text-white' : 'text-current'
])

const textClasses = computed(() => [
  'text-sm font-semibold truncate',
  props.item.active ? 'text-white' : 'text-current'
])
</script>
