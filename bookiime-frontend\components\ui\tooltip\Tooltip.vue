<template>
  <div class="relative inline-block">
    <div
      ref="triggerRef"
      @mouseenter="showTooltip"
      @mouseleave="hideTooltip"
      @focus="showTooltip"
      @blur="hideTooltip"
    >
      <slot />
    </div>
    
    <Teleport to="body">
      <Transition
        enter-active-class="transition ease-out duration-200"
        enter-from-class="opacity-0 scale-95"
        enter-to-class="opacity-100 scale-100"
        leave-active-class="transition ease-in duration-150"
        leave-from-class="opacity-100 scale-100"
        leave-to-class="opacity-0 scale-95"
      >
        <div
          v-if="isVisible"
          ref="tooltipRef"
          :class="cn(
            'absolute z-50 px-3 py-1.5 text-sm text-white bg-neutral-900 rounded-md shadow-lg',
            'whitespace-nowrap pointer-events-none',
            $attrs.class as string
          )"
          :style="tooltipStyle"
        >
          <slot name="content">
            {{ content }}
          </slot>
          <div
            :class="cn(
              'absolute w-2 h-2 bg-neutral-900 rotate-45',
              arrowClasses
            )"
          />
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { cn } from '@/lib/utils'

interface Props {
  content?: string
  side?: 'top' | 'right' | 'bottom' | 'left'
  align?: 'start' | 'center' | 'end'
  delayDuration?: number
}

const props = withDefaults(defineProps<Props>(), {
  side: 'top',
  align: 'center',
  delayDuration: 700
})

const isVisible = ref(false)
const triggerRef = ref<HTMLElement>()
const tooltipRef = ref<HTMLElement>()
const timeoutId = ref<NodeJS.Timeout>()

const tooltipStyle = ref({})

const arrowClasses = computed(() => {
  const side = props.side
  const align = props.align
  
  const sideClasses = {
    top: '-bottom-1',
    right: '-left-1 top-1/2 -translate-y-1/2',
    bottom: '-top-1',
    left: '-right-1 top-1/2 -translate-y-1/2'
  }
  
  const alignClasses = {
    start: side === 'top' || side === 'bottom' ? 'left-2' : 'top-2',
    center: side === 'top' || side === 'bottom' ? 'left-1/2 -translate-x-1/2' : 'top-1/2 -translate-y-1/2',
    end: side === 'top' || side === 'bottom' ? 'right-2' : 'bottom-2'
  }
  
  return `${sideClasses[side]} ${alignClasses[align]}`
})

const updateTooltipPosition = () => {
  if (!triggerRef.value || !tooltipRef.value) return
  
  const triggerRect = triggerRef.value.getBoundingClientRect()
  const tooltipRect = tooltipRef.value.getBoundingClientRect()
  
  let top = 0
  let left = 0
  
  switch (props.side) {
    case 'top':
      top = triggerRect.top - tooltipRect.height - 8
      break
    case 'right':
      top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2
      left = triggerRect.right + 8
      break
    case 'bottom':
      top = triggerRect.bottom + 8
      break
    case 'left':
      top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2
      left = triggerRect.left - tooltipRect.width - 8
      break
  }
  
  switch (props.align) {
    case 'start':
      if (props.side === 'top' || props.side === 'bottom') {
        left = triggerRect.left
      }
      break
    case 'center':
      if (props.side === 'top' || props.side === 'bottom') {
        left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2
      }
      break
    case 'end':
      if (props.side === 'top' || props.side === 'bottom') {
        left = triggerRect.right - tooltipRect.width
      }
      break
  }
  
  tooltipStyle.value = {
    top: `${top}px`,
    left: `${left}px`
  }
}

const showTooltip = () => {
  if (timeoutId.value) {
    clearTimeout(timeoutId.value)
  }
  
  timeoutId.value = setTimeout(() => {
    isVisible.value = true
    nextTick(() => {
      updateTooltipPosition()
    })
  }, props.delayDuration)
}

const hideTooltip = () => {
  if (timeoutId.value) {
    clearTimeout(timeoutId.value)
  }
  isVisible.value = false
}

onUnmounted(() => {
  if (timeoutId.value) {
    clearTimeout(timeoutId.value)
  }
})
</script>
