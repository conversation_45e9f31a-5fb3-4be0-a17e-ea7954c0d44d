<template>
  <aside :class="sidebarClasses">

    <!-- Logo Section -->
    <div class="p-3">
      <Card :class="[
        'backdrop-blur-md shadow-lg border-white/20 transition-all duration-300 bg-white/80',
        collapsed ? 'p-3' : 'p-4'
      ]">
        <CardContent :class="[
          'p-0',
          collapsed ? 'flex items-center justify-center' : 'flex items-center justify-between'
        ]">
          <NuxtLink to="/dashboard" :class="collapsed ? 'flex items-center justify-center' : 'flex items-center space-x-3'">
            <div class="w-10 h-10 primary-background rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm">
              <img src="/favicon.png" alt="Bookiime" class="w-6 h-6 object-contain" />
            </div>
            <span v-if="!collapsed" class="text-xl font-bold text-neutral-900 transition-all duration-300">
              Bookiime
            </span>
          </NuxtLink>

          <!-- Alternative collapse button for expanded state -->
          <Button
            v-if="!collapsed"
            @click="$emit('toggle-collapse')"
            variant="ghost"
            size="sm"
            class="hidden lg:flex p-2 rounded-lg hover:bg-white/50 transition-colors"
          >
            <Icon name="lucide:panel-left-close" class="w-5 h-5 text-neutral-600" />
          </Button>
        </CardContent>

        <!-- Close button for mobile -->
        <Button
          v-if="show"
          @click="$emit('close')"
          variant="ghost"
          size="sm"
          class="absolute top-3 right-3 p-2 rounded-lg hover:bg-white/50 transition-colors lg:hidden"
        >
          <Icon name="lucide:x" class="w-5 h-5 text-neutral-600" />
        </Button>
      </Card>
    </div>

    <!-- Navigation Section - Dynamic Island -->
    <div class="flex-1 px-3 py-8 flex flex-col justify-center">
      <nav :class="[
        'backdrop-blur-xl rounded-3xl shadow-2xl border border-white/30 transition-all duration-500 ease-out transform hover:scale-[1.02]',
        'bg-gradient-to-br from-white/95 via-white/90 to-white/85',
        'relative overflow-hidden',
        collapsed ? 'px-2 py-3' : 'px-3 py-4'
      ]">
        <!-- Dynamic Island Glow Effect -->
        <div class="absolute inset-0 bg-gradient-to-br from-primary-500/10 via-transparent to-primary-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

        <!-- Navigation Items Container -->
        <div class="relative z-10 flex flex-col" :class="collapsed ? 'space-y-1' : 'space-y-2'">
          <dashboard-sidebar-item
            v-for="item in navigationItems"
            :key="item.route"
            :item="item"
            :collapsed="collapsed"
            @click="$emit('close')"
          />
        </div>

        <!-- Dynamic Island Shimmer Effect -->
        <div class="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-700">
          <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 animate-shimmer"></div>
        </div>
      </nav>
    </div>

    <!-- User Section -->
    <div class="p-3">
      <Card :class="[
        'backdrop-blur-md shadow-lg border-white/20 transition-all duration-300 bg-white/80',
        collapsed ? 'p-3' : 'p-4'
      ]">
        <CardContent class="p-0">
          <dashboard-sidebar-user
            :user="user"
            :collapsed="collapsed"
            @logout="$emit('logout')"
          />
        </CardContent>
      </Card>
    </div>
  </aside>
</template>

<script setup lang="ts">
interface NavigationItem {
  icon: string
  label: string
  route: string
  active?: boolean
}

interface User {
  id?: string
  name?: string
  email?: string
  avatar?: string
}

interface Props {
  show?: boolean
  collapsed: boolean
  navigationItems: NavigationItem[]
  user: User | null
}

interface Emits {
  (e: 'close'): void
  (e: 'logout'): void
  (e: 'toggle-collapse'): void
}

const props = withDefaults(defineProps<Props>(), {
  show: false
})
defineEmits<Emits>()

// Computed classes for sidebar
const sidebarClasses = computed(() => {
  const baseClasses = 'fixed left-0 top-0 z-50 h-screen transition-all duration-300 flex flex-col'

  return [
    baseClasses,
    // Background: glassy effect for all states, no shadow on wide screens
    'bg-white/10 backdrop-blur-xl border-r border-white/20',
    // Shadow only on mobile/tablet
    'shadow-2xl lg:shadow-none',
    // Mobile: always show full width with text (never collapsed on mobile)
    'w-80', // Slightly wider on mobile for better touch targets and floating cards
    props.show ? 'translate-x-0' : '-translate-x-full',
    // Desktop: always visible, width based on collapsed state
    'lg:translate-x-0',
    props.collapsed ? 'lg:w-20' : 'lg:w-72' // Increased width to accommodate floating cards
  ]
})
</script>

<style scoped>
@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
  }
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

/* Enhanced hover effects for navigation */
nav:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.4);
}

/* Dynamic Island breathing effect */
nav {
  animation: breathe 4s ease-in-out infinite;
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.005);
  }
}
</style>
